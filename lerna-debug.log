0 silly argv {
0 silly argv   _: [ 'run' ],
0 silly argv   scope: '@smartdesk/design',
0 silly argv   lernaVersion: '8.2.2',
0 silly argv   '$0': 'node_modules/lerna/dist/cli.js',
0 silly argv   script: 'build:lib:dev'
0 silly argv }
1 notice cli v8.2.2
2 verbose packageConfigs Explicit "packages" configuration found in lerna.json. Resolving packages using the configured glob(s): ["packages/*"]
3 verbose rootPath /Users/<USER>/Projects/nc/smart-desk/smartdesk-portal
4 notice filter including "@smartdesk/design"
5 info filter [ '@smartdesk/design' ]
6 verbose run Nx target configuration was not found. Task dependencies will not be automatically included.
7 error - packages/admin/package.json: Error: ValueExpected in /Users/<USER>/Projects/nc/smart-desk/smartdesk-portal/packages/admin/package.json at 1:1
7 error   [0m[31m[1m>[22m[39m[90m 1 | [39m[0m
7 error   [0m [90m   | [39m[31m[1m^[22m[39m[0m
7 error
7 error       at parseJson (/Users/<USER>/Projects/nc/smart-desk/smartdesk-portal/node_modules/.pnpm/nx@20.8.2/node_modules/nx/src/utils/json.js:29:15)
7 error       at readJsonFile (/Users/<USER>/Projects/nc/smart-desk/smartdesk-portal/node_modules/.pnpm/nx@20.8.2/node_modules/nx/src/utils/fileutils.js:32:37)
7 error       at createNodeFromPackageJson (/Users/<USER>/Projects/nc/smart-desk/smartdesk-portal/node_modules/.pnpm/nx@20.8.2/node_modules/nx/src/plugins/package-json/create-nodes.js:83:47)
7 error       at /Users/<USER>/Projects/nc/smart-desk/smartdesk-portal/node_modules/.pnpm/nx@20.8.2/node_modules/nx/src/plugins/package-json/create-nodes.js:40:20
7 error       at /Users/<USER>/Projects/nc/smart-desk/smartdesk-portal/node_modules/.pnpm/nx@20.8.2/node_modules/nx/src/project-graph/plugins/utils.js:10:33
7 error       at Array.map (<anonymous>)
7 error       at createNodesFromFiles (/Users/<USER>/Projects/nc/smart-desk/smartdesk-portal/node_modules/.pnpm/nx@20.8.2/node_modules/nx/src/project-graph/plugins/utils.js:8:35)
7 error       at exports.createNodesV2 (/Users/<USER>/Projects/nc/smart-desk/smartdesk-portal/node_modules/.pnpm/nx@20.8.2/node_modules/nx/src/plugins/package-json/create-nodes.js:33:51)
7 error       at LoadedNxPlugin.createNodes (/Users/<USER>/Projects/nc/smart-desk/smartdesk-portal/node_modules/.pnpm/nx@20.8.2/node_modules/nx/src/project-graph/plugins/loaded-nx-plugin.js:26:65)
