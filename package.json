{"name": "@smartdesk/lerna-portal", "version": "1.0.0", "private": true, "workspaces": ["packages/*"], "scripts": {"build:preview:dev": "lerna run build:dev --scope=@smartdesk/preview", "build:preview:test": "lerna run build:test --scope=@smartdesk/preview", "build:preview:prod": "lerna run build:prod --scope=@smartdesk/preview", "build:lib:preview:dev": "lerna run build:lib:dev --scope=@smartdesk/preview", "build:lib:preview:test": "lerna run build:lib:test --scope=@smartdesk/preview", "build:lib:preview:prod": "lerna run build:lib:prod --scope=@smartdesk/preview", "dev:preview:dev": "lerna run dev:dev --scope=@smartdesk/preview", "dev:preview:test": "lerna run dev:test --scope=@smartdesk/preview", "dev:preview:prod": "lerna run dev:prod --scope=@smartdesk/preview", "build:design:dev": "pnpm run build:lib:preview:dev && lerna run build:dev --scope=@smartdesk/design", "build:design:test": "pnpm run build:lib:preview:test && lerna run build:test --scope=@smartdesk/design", "build:design:prod": "pnpm run build:lib:preview:prod && lerna run build:prod --scope=@smartdesk/design", "build:lib:design:dev": "pnpm run build:lib:preview:dev && lerna run build:lib:dev --scope=@smartdesk/design", "build:lib:design:test": "pnpm run build:lib:preview:test && lerna run build:lib:test --scope=@smartdesk/design", "build:lib:design:prod": "pnpm run build:lib:preview:prod && lerna run build:lib:prod --scope=@smartdesk/design", "dev:design:dev": "pnpm run build:lib:preview:dev && lerna run dev:dev --scope=@smartdesk/design", "dev:design:test": "pnpm run build:lib:preview:test && lerna run dev:test --scope=@smartdesk/design", "dev:design:prod": "pnpm run build:lib:preview:prod && lerna run dev:prod --scope=@smartdesk/design", "build:admin:dev": "pnpm run build:lib:design:dev && lerna run build:dev --scope=@smartdesk/admin", "build:admin:test": "pnpm run build:lib:design:test && lerna run build:test --scope=@smartdesk/admin", "build:admin:prod": "pnpm run build:lib:design:prod && lerna run build:prod --scope=@smartdesk/admin", "build:lib:admin:dev": "pnpm run build:lib:design:dev && lerna run build:lib:dev --scope=@smartdesk/admin", "build:lib:admin:test": "pnpm run build:lib:design:test && lerna run build:lib:test --scope=@smartdesk/admin", "build:lib:admin:prod": "pnpm run build:lib:design:prod && lerna run build:lib:prod --scope=@smartdesk/admin", "dev:admin:dev": "pnpm run build:lib:design:dev && lerna run dev:dev --scope=@smartdesk/admin", "dev:admin:test": "pnpm run build:lib:design:test && lerna run dev:test --scope=@smartdesk/admin", "dev:admin:prod": "pnpm run build:lib:design:prod && lerna run dev:prod --scope=@smartdesk/admin", "build:base:dev": "pnpm run build:lib:admin:dev && lerna run build:dev --scope=@smartdesk/base", "build:base:test": "pnpm run build:lib:admin:test && lerna run build:test --scope=@smartdesk/base", "build:base:prod": "pnpm run build:lib:admin:prod && lerna run build:prod --scope=@smartdesk/base", "dev:base:dev": "pnpm run build:lib:admin:dev && lerna run dev:dev --scope=@smartdesk/base", "dev:base:test": "pnpm run build:lib:admin:test && lerna run dev:test --scope=@smartdesk/base", "dev:base:prod": "pnpm run build:lib:admin:prod && lerna run dev:prod --scope=@smartdesk/base", "watch": "chokidar 'packages/*/src/**/*' --extensions 'ts,tsx,js,jsx,vue' --ignore '**/node_modules/**' --throttle 300 --initial --command 'pnpm run build:lib:admin:dev'", "dev": "pnpm run dev:base:dev", "build": "pnpm run build:base:prod", "clean": "lerna clean", "prepare": "husky install", "type-check": "lerna run type-check --stream", "type-check:watch": "lerna run type-check:watch --stream --parallel"}, "packageManager": "pnpm@10.7.1", "devDependencies": {"@types/node": "^22.15.21", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "chokidar-cli": "^3.0.0", "concurrently": "^9.1.2", "husky": "^8.0.3", "lerna": "^8.2.2", "typescript": "^5.8.3"}, "dependencies": {"@chances/portal_common_core": "1.0.20250529141211", "pinia": "2.3.1", "vue": "3.5.13", "vue-router": "4.5.0"}, "overrides": {"vue": "3.5.13", "vue-router": "4.5.0", "pinia": "2.3.1", "@chances/portal_common_core": "1.0.20250529141211"}}