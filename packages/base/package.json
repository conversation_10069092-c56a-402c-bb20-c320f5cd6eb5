{"name": "@smartdesk/base", "version": "1.0.0", "type": "module", "scripts": {"dev:dev": "vite --mode development", "dev:test": "vite --mode test", "dev:prod": "vite --mode production", "build:dev": "vue-tsc --build && vite build --mode development", "build:test": "vue-tsc --build && vite build --mode test", "build:prod": "vue-tsc --build && vite build --mode production --emptyOutDir --outDir=../../dist", "type-check": "vue-tsc --build", "type-check:watch": "vue-tsc --build --watch"}, "dependencies": {"@chances/audit_publish_portal": "1.0.20250612144126", "@chances/iam_portal": "1.0.20250612152247", "@element-plus/icons-vue": "^2.3.1", "@iconify/vue": "^4.3.0", "@smartdesk/admin": "workspace:*", "@smartdesk/design": "workspace:*", "@smartdesk/preview": "workspace:*", "axios": "^1.9.0", "crypto-js": "^4.2.0", "cs-common-components": "^0.0.8", "element-plus": "^2.9.10", "lodash": "^4.17.21"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@babel/parser": "^7.27.2", "@babel/traverse": "^7.27.1", "@rollup/plugin-json": "^6.1.0", "@types/babel__traverse": "^7.20.7", "@types/node": "^20.17.50", "@vitejs/plugin-vue": "^5.2.4", "@vue/compiler-sfc": "^3.5.14", "less": "^4.3.0", "less-loader": "^12.3.0", "mitt": "^3.0.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.4", "typescript": "^5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.10"}, "peerDependencies": {"@chances/portal_common_core": "1.0.20250529141211", "pinia": "2.3.1", "vue": "3.5.13", "vue-router": "4.5.0"}}