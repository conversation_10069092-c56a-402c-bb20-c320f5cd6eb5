import { createApp } from 'vue';
import App from './App.vue';

// ARCO UI
import ArcoVue from '@arco-design/web-vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import '@arco-design/web-vue/dist/arco.less';

// Element Plus UI
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';

// ICON UI
import { Icon } from '@iconify/vue';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

// 路由
import { router } from './router';

// 公共依赖
import {
    createPermissionDirective,
    createPersistPlugin,
    emitter,
    installAllStores,
    pinia,
    STORAGE_DRIVER,
    vTrim,
} from '@chances/portal_common_core';
import '@chances/portal_common_core/dist/style.css';

// pinia
import { getActivePinia, setActivePinia } from 'pinia';

// 公共组件
import CsCommonComponents from 'cs-common-components';
import 'cs-common-components/dist/style.css';

// 依赖样式
import '@smartdesk/preview/dist/style.css';
import '@smartdesk/design/dist/style.css';
import '@smartdesk/admin/dist/style.css';
import '@chances/iam_portal/dist/style.css';
import '@chances/audit_publish_portal/dist/style.css';
import '@chances/audit_publish_portal/dist/audit_publish_portal_theme.less';

// 依赖组件、状态
import { components as preview_components, installPreviewStores } from '@smartdesk/preview';
import { components as design_components, installDesignerStores } from '@smartdesk/design';
import { components as admin_components, installAdminStores } from '@smartdesk/admin';

export const dynamicComponents = [
    preview_components,
    design_components,
    admin_components,
];

// 创建 APP 实例
const app = createApp(App);

// 指令
app.directive('trim', vTrim);

// 设置 pinia 实例为当前可用 pinia 实例
setActivePinia(pinia);

// 创建 pinia 持久化插件
const persistPlugin = createPersistPlugin({
    defaultDatabase: 'smartdesk',
    defaultDriver: STORAGE_DRIVER.LOCAL_STORAGE,
    timeout: 10000,
});

app.use(pinia);

// 注册持久化插件
app.use(persistPlugin);

// 全局拦截组件生命周期，动态绑定 Pinia 实例
app.mixin({
    setup() {
        if (!getActivePinia()) {
            setActivePinia(pinia);
        }
    },
});

// 权限指令
app.directive('permission', createPermissionDirective({ mode: 'disable' }));

// ARCO UI
app.use(ArcoVue).use(ArcoVueIcon);

// Element Plus UI
app.use(ElementPlus);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}

// ICON UI
app.component('Icon', Icon);

// 公共组件 UI
app.use(CsCommonComponents);

// 注册公共依赖 store
installAllStores(pinia);

// 注册依赖组件
dynamicComponents.forEach(dynamicComponent => app.use(dynamicComponent))

// 注册依赖 store
installPreviewStores(pinia);
installDesignerStores(pinia);
installAdminStores(pinia);

// 将 Mitt 实例注入到全局属性中
app.config.globalProperties.$emitter = emitter;

// 注册路由
(window as any).$vueRouter = router;
app.use(router);

// 挂载 app 组件
app.mount('#app');
