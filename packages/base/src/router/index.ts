import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import { useMenuStore, useOperatorStore } from '@chances/portal_common_core';
import { routes as preview_routes } from '@smartdesk/preview';
import { routes as design_routes } from '@smartdesk/design';
import { routes as admin_routes } from '@smartdesk/admin';
import { routes as iam_routes } from '@chances/iam_portal';
import { routes as audit_publish_routes } from '@chances/audit_publish_portal';

//  动态路由
const dynamicRoutes: RouteRecordRaw[] = [
    ...preview_routes,
    ...design_routes,
    ...admin_routes,
    ...iam_routes,
    ...audit_publish_routes,
];

// 基座项目路由
const baseRoutes: RouteRecordRaw[] = [
    // iframe 组件路由
    {
        path: '/iframe',
        name: 'BaseIframe',
        component: () => import('../components/base-iframe.vue'),
        props: route => ({ url: route.query.url as string }),
        children: [],
    },
    // Forbidden 组件路由
    {
        path: '/forbidden',
        name: 'Forbidden',
        component: () => import('../views/Forbidden.vue'),
    },
    // Login 组件路由
    {
        path: '/login',
        name: 'Login',
        component: () => import('../views/login/index.vue'),
    },
];

const whiteList = ['/iframe', '/forbidden', '/login'];

dynamicRoutes.push(...baseRoutes);


// 处理路由及其子路由，防止 name、path 重复
const processRoutes = (
    routes: RouteRecordRaw[],
    parentPath: string = '',
    parentName: string = '',
): RouteRecordRaw[] => {
    return routes.map(route => {
        const cleanedPath = route.path.replace(/^\//, '');
        const newPath = `${parentPath}/${cleanedPath}`.replace(/\/+/g, '/').replace(/\/$/, '');

        return {
            ...route,
            path: cleanedPath,
            name: `${parentName}-${route.name as string}`,
            children: route.children
                ? processRoutes(route.children, newPath, route.name?.toString())
                : [],
        };
    });
};

// 加载路由，设置布局
const loadLayouts = (): RouteRecordRaw[] => {
    try {
        const routes: RouteRecordRaw[] = [];
        const layoutGroups = new Map<string, RouteRecordRaw[]>();

        // 获取主布局配置
        const mainLayout = 'sider-header-content-layout';

        // 分组处理：根据 meta.layout 分组路由
        dynamicRoutes.forEach(route => {
            const layoutName = route.meta?.layout as string || mainLayout;
            const group = layoutGroups.get(layoutName) || [];
            group.push(route);
            layoutGroups.set(layoutName, group);
        });

        // 为每个布局创建父路由
        layoutGroups.forEach((routesInGroup, layoutName) => {
            const layoutPath = layoutName === mainLayout ? '/' : `/${layoutName}`;

            routesInGroup.forEach((route) => {
                route.path = route.path.replace(layoutName + '/', '');
            });

            const layoutRoute: RouteRecordRaw = {
                path: layoutPath,
                component: () => import(`../layouts/${layoutName}.vue`),
                children: processRoutes(
                    routesInGroup.filter(route =>
                        !['Login'].includes(route.name?.toString() || ''),
                    ),
                    '',  // 重置父路径
                    layoutName,  // 使用布局名作为前缀
                ),
            };

            routes.push(layoutRoute);
        });

        // 添加不需要布局的基础路由
        return [...routes, ...baseRoutes];
    } catch (error) {
        console.error('路由初始化失败:', error);
        return baseRoutes;
    }
};


// 初始化路由
const initRouter = () => {
    const routes: Array<RouteRecordRaw> = loadLayouts();

    const router = createRouter({
        history: createWebHistory(),
        routes: routes,
    });

    router.beforeEach((to, from, next) => {

        // 校验是否登录，未登录则路由到登录页
        if (to.path !== '/login' && !useOperatorStore().getOperator().userId) {
            next({ path: '/login' });
        }

        if (to.path === '/' && useOperatorStore().getOperator().userId) {
            const menus = useMenuStore().getMenus() ?? [];
            const [firstMenu = {} as any] = menus;
            const [firstChildMenu = {}] = firstMenu.children ?? [];

            if (firstChildMenu.path) {
                next({ path: firstChildMenu.path });
            } else {
                // 若不存在子菜单路径，默认处理
                next();
            }
        }

        // 如果访问没有的菜单，则跳转到 Forbidden 页面
        if (Object.keys(useMenuStore().menuMap).includes(to.path) || whiteList.includes(to.path)) {
            next();
        } else {
            next({ path: '/forbidden' });
        }
    });

    return router;
};

export const router = initRouter();
