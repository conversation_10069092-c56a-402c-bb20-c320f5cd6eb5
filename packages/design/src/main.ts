import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';

import { Icon } from '@iconify/vue';

import '@/styles/styles.scss';
import '@/styles/tailwind.css';

import { setActivePinia } from 'pinia';

import { createPersistPlugin, emitter, installAllStores, pinia, STORAGE_DRIVER } from '@chances/portal_common_core';
import '@chances/portal_common_core/dist/style.css';

import icons from '@/utils/install-icons.ts';

import installDesignerStores from '@/stores';
import installDesignerComponents from '@/components';

// 设计器
import { components as preview_components, installPreviewStores } from '@smartdesk/preview';
import '@smartdesk/preview/dist/style.css';

export const dynamicComponents = [
    preview_components,
];



// 创建 APP 实例
const app = createApp(App);

// 注册 ElementPlus 相关组件、图标
app.use(ElementPlus);
app.use(icons);

// 注册 iconify 组件
app.component('Icon', Icon);

// 注册自定义组件
app.use(installDesignerComponents);

// // 注册 EPG 组件
// app.use(installEpgComponents);
// // 注册 jsview mock 组件
// app.use(installJsViewMock);

// 设置 pinia 实例为当前可用 pinia 实例
setActivePinia(pinia);

// 创建 pinia 持久化插件
const persistPlugin = createPersistPlugin({
    defaultDatabase: 'smartdesk-design',
    defaultDriver: STORAGE_DRIVER.LOCAL_STORAGE,
    timeout: 10000,
});

// 注册 pinia
app.use(pinia);

// 注册持久化插件
app.use(persistPlugin);

// 注册所有 store
installDesignerStores(pinia);

// 注册公共依赖 store
installAllStores();

// 注册依赖组件
dynamicComponents.forEach(dynamicComponent => app.use(dynamicComponent));

// 注册依赖 store
installPreviewStores(pinia);

// 将 Mitt 实例注入到全局属性中
app.config.globalProperties.$emitter = emitter;

// 注册路由
(window as any).$vueRouter = router;
app.use(router);

// 挂载实例
app.mount('#app');
