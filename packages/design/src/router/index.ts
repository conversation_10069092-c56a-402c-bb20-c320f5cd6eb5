import type { RouteRecordRaw } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';
import { scopedStyleWrapper } from '@/utils/scoped-style-wrapper';
import { routes as _chances_visual_preview_routes } from '@smartdesk/preview';

const routes: Array<RouteRecordRaw> = [
    {
        path: '/page_designer',
        name: '页面设计器',
        meta: {
            componentName: 'PageDesigner',
            layout: 'designer',
        },
        component: scopedStyleWrapper(() => import('@/pages/page/page-designer.vue'), 'PageDesigner'),
    },
    {
        path: '/section_designer',
        name: '楼层定义设计器',
        meta: {
            componentName: 'SectionDesigner',
            layout: 'designer',
        },
        component: scopedStyleWrapper(() => import('@/pages/section/section-designer.vue'), 'SectionDesigner'),
    }
];

// 加上布局
_chances_visual_preview_routes.forEach((route: RouteRecordRaw) => {
    if (route.meta?.layout) {
        route.path = '/' + route.meta.layout + route.path;
    }
});

// 总路由
const allRoutes: Array<RouteRecordRaw> = [
    ...routes,
    ..._chances_visual_preview_routes,
];

const router = createRouter({
    history: createWebHistory(),
    routes: allRoutes,
});

export { routes };
export default router;
