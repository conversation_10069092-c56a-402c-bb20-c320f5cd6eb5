import { defineStore, StateTree } from 'pinia';
import { STORAGE_DRIVER } from '@chances/portal_common_core';
import { ComponentStyle, DesignMode, LayoutFile, Page, PageCell, PageCellItem, PageSection, Section } from '@/types';
import { computed, nextTick, reactive, ref } from 'vue';
import { cellApi, pageApi, pageSectionApi } from '@/utils/http/register.ts';
import { useFeedback } from '@/composables';
import { CELL_ITEM_TYPE } from '@/utils/constants';

// 元素类型：桌面、页面、页面楼层、坑位
export type ElementType = 'Desktop' | 'Page' | 'PageSection' | 'Cell';

// 页面设计器状态存储
export const usePageDesignerStore = defineStore('page-designer-store', () => {

    const feedback = useFeedback();

    // 模式：页面设计器、桌面设计器；默认为页面设计器
    const mode = ref<'page' | 'desktop'>('page');

    // 桌面编码
    const desktopCode = ref<string>('');

    // 当前导航编码
    const navCode = ref<string>('');

    // 当前页面实体
    const page = ref<Page>({} as Page);

    // 元素 Map
    const elementMap = reactive<Map<string, any>>(new Map());

    // 坑位到选中坑位元素的映射
    const selectedCellItemMap = reactive<Map<string, string>>(new Map());

    // 选中的元素编码
    const selectedElementCode = ref<string>();

    // 选中元素的类型，默认选中页面类型
    const selectedElementType = ref<ElementType>();

    // 是否展示预删除的页面楼层
    const showDeletedPageSections = ref<boolean>(false);

    // 选中的元素
    const selectedElement = computed({
        get: () => {
            if (selectedElementCode.value) {
                return elementMap.get(selectedElementCode.value);
            }
        },
        set: (value) => {
            if (selectedElementCode.value) {
                elementMap.set(selectedElementCode.value, value);
            }
        }
    });

    // 选中的页面楼层
    const selectedPageSection = computed<PageSection>({
        get: () => {
            if (selectedElementType.value === 'PageSection') {
                return selectedElement.value;
            }
        },
        set: (value) => {
            if (selectedElementType.value === 'PageSection') {
                selectedElement.value = value;
            }
        },
    });

    // 选中的楼层坑位
    const selectedPageCell = computed<PageCell>({
        get: () => {
            if (selectedElementType.value === 'Cell') {
                return selectedElement.value;
            }
        },
        set: (value) => {
            if (selectedElementType.value === 'Cell') {
                selectedElement.value = value;
            }
        },
    });

    // 选中的坑位元素
    const selectedPageCellItem = computed<PageCellItem | undefined>(() => {
        if (!selectedPageCell.value) return undefined;

        const cellCode = selectedPageCell.value.code;
        const items = selectedPageCell.value.pageCellItemList || [];

        // 优先使用用户之前选中的坑位元素
        const userSelectedCode = selectedCellItemMap.get(cellCode);
        if (userSelectedCode) {
            const userSelected = items.find(item => item.code === userSelectedCode);
            if (userSelected) return userSelected;
        }

        // 其次选择默认坑位元素
        const defaultItem = items.find(item => item.defaultFlag === CELL_ITEM_TYPE.DEFAULT);
        if (defaultItem) return defaultItem;

        // 最后选择第一个坑位元素
        return items.length > 0 ? items[0] : undefined;
    });

    // 获取当前元素类型
    const getElementTypeName = () => {
        if (!selectedElementType.value) {
            return '';
        }

        switch (selectedElementType.value) {
            case 'Desktop':
                return '桌面';
            case 'Page':
                return '页面';
            case 'PageSection':
                return '页面楼层';
            case 'Cell':
                return '坑位';
            default:
                return '';
        }
    };

    // 获取页面实体，设置元素 Map
    const getPage = async (code: string) => {
        const res = await pageApi.getPage(code);
        if (res.code === 200) {
            page.value = res.result;
            buildElementMap();
        }
    };

    // 获取页面楼层
    const getPageSection = async (pageSectionCode: string) => {
        const res = await pageSectionApi.getPageSectionByCode(pageSectionCode);
        if (res.code === 200) {
            elementMap.set(pageSectionCode, res.result);
        }
        return res;
    };

    // 更新页面楼层
    const updatePageSection = async (pageSection: PageSection) => {
        const res = await pageSectionApi.updatePageSectionData(pageSection.code, pageSection);
        if (res.code === 200) {
            feedback.success('页面楼层更新成功');
            await refreshPageData(page.value.code);
        } else {
            feedback.error('页面楼层更新失败：' + res.msg);
        }
        return res;
    };

    // 更新坑位
    const updatePageCell = async (pageCell: PageCell) => {
        const res = await cellApi.updatePageCellData(pageCell.code, pageCell);
        if (res.code === 200) {
            feedback.success('坑位更新成功');
            await refreshPageData(page.value.code);
        } else {
            feedback.error('坑位更新失败：' + res.msg);
        }
        return res;
    };

    // 构建元素 Map
    const buildElementMap = () => {
        elementMap.clear();

        // 设置页面
        elementMap.set(page.value.code, page.value);

        page.value.pageSectionList?.forEach(pageSection => {
            // 设置页面楼层
            elementMap.set(pageSection.code, pageSection);

            pageSection.pageCellList?.forEach(pageCell => {
                // 确保坑位有默认坑位元素
                ensureDefaultCellItem(pageCell);
                // 对坑位元素进行排序
                sortCellItems(pageCell);
                // 设置楼层坑位
                elementMap.set(pageCell.code, pageCell);

                // pageCell.pageCellItemList.forEach(pageCellItem => {
                //     // 设置坑位元素
                //     elementMap.set(pageCellItem.code, pageCellItem);
                // });
            });
        });
    };

    // 更新页面楼层排序
    const updatePageSectionOrderNo = async () => {
        const res = await pageSectionApi.updatePageSectionOrder(page.value.pageSectionList);
        if (res.code === 200) {
            feedback.success('保存页面楼层排序成功');
            await refreshPageData(page.value.code);
        } else {
            feedback.error('保存页面楼层排序失败：' + res.msg);
        }
    };

    // 更新页面数据
    const refreshPageData = async (code: string) => {
        await getPage(code);
    };

    // 刷新页面
    const refreshPage = async (code: string) => {
        await refreshPageData(code);

        if (mode.value === 'desktop') {
            switchElement('Desktop', code);
        } else {
            switchElement('Page', code);
        }
    };

    // 更新样式信息
    const updateProps = (value: Record<string, any>) => {
        selectedElement.value.layout.props = value;
    };

    // 更新元素信息
    const updateElement = async () => {
        if (!selectedElementCode.value) {
            return;
        }

        let res: any;
        switch (selectedElementType.value) {
            case 'Desktop':
                res = await pageApi.updatePage(selectedElementCode.value, selectedElement.value);
                break;
            case 'Page':
                res = await pageApi.updatePage(selectedElementCode.value, selectedElement.value);
                break;
            case 'PageSection':
                res = await pageSectionApi.updatePageSectionData(selectedElementCode.value, selectedElement.value);
                break;
            case 'Cell':
                res = await cellApi.updatePageCellData(selectedElementCode.value, selectedElement.value);
                break;
        }

        if (res && res.code === 200) {
            feedback.success('更新成功');
            await refreshPageData(page.value.code);
        } else {
            feedback.error('更新失败');
        }

        return res;
    };

    // 批量更新坑位样式信息
    const batchUpdateCellLayoutProps = async (pageCells: PageCell[], value: Record<string, any>) => {
        pageCells.forEach(pageCell => {
            pageCell.layout.props = value;
        });

        // 将 pageCells 映射为codeLayoutMap
        const codeLayoutMap: Record<string, LayoutFile> = {};
        pageCells.forEach(pageCell => {
            codeLayoutMap[pageCell.code] = pageCell.layout;
        });

        const res = await cellApi.batchUpdatePageCellLayout(codeLayoutMap);
        if (res.code === 200) {
            feedback.success('批量更新坑位样式成功');
            await refreshPageData(page.value.code);
        } else {
            feedback.error('批量更新坑位样式失败：' + res.msg);
        }
    };

    // 更新坑位组件
    const updateCellComponent = async (componentStyle: ComponentStyle) => {
        if (!selectedElementCode.value) {
            return;
        }

        // 更新组件类型、样式 props
        selectedElement.value.componentType = componentStyle.type;
        selectedElement.value.layout.props = componentStyle.layout;

        // 调用接口
        const res = await cellApi.updatePageCellComponent(selectedElementCode.value, selectedElement.value);
        if (res.code === 200) {
            feedback.success('更新坑位组件成功');
            await refreshPageData(page.value.code);
        } else {
            feedback.error('更新坑位组件失败：' + res.msg);
        }
    };

    // 切换模式
    const switchMode = (modeForm: 'page' | 'desktop') => {
        mode.value = modeForm;
    };

    // 设置页面所属的导航和导航所属的桌面
    const setNavAndDesktop = (navCodeForm: string, desktopCodeForm: string) => {
        navCode.value = navCodeForm;
        desktopCode.value = desktopCodeForm;
    };

    // 切换元素
    const switchElement = (type: ElementType, code: string) => {
        selectedElementType.value = type;
        selectedElementCode.value = code;
    };

    // 切换坑位元素
    const switchPageCellItem = (itemCode: string) => {
        if (selectedPageCell.value) {
            selectedCellItemMap.set(selectedPageCell.value.code, itemCode);
        }
    };

    // 切换是否展示预删除的页面楼层
    const switchDeletedPageSections = () => {
        showDeletedPageSections.value = !showDeletedPageSections.value;
    };

    // 新增页面楼层
    const addPageSection = async (sourceCode: string, targetCode: string) => {
        const res = await pageSectionApi.addPageSection(sourceCode, targetCode);
        if (res.code === 200) {
            feedback.success('新增页面楼层成功');
            await refreshPageData(page.value.code);
        } else {
            feedback.error('新增页面楼层失败：' + res.msg);
        }
    };

    // 更换页面楼层
    const changePageSection = async (sourceCode: string, targetCode: string) => {
        const res = await pageSectionApi.changePageSection(sourceCode, targetCode);
        if (res.code === 200) {
            feedback.success('更换页面楼层成功');
            await refreshPageData(page.value.code);
        } else {
            feedback.error('更换页面楼层失败：' + res.msg);
        }
    };

    // 页面楼层另存为楼层定义
    const pageSectionSaveAsSection = async (pageSectionCode: string, sectionForm: Section) => {
        const res = await pageSectionApi.saveAsSection(pageSectionCode, sectionForm);
        if (res.code === 200) {
            feedback.success('页面楼层另存为楼层定义成功');
            await refreshPageData(page.value.code);
        } else {
            feedback.error('页面楼层另存为楼层定义失败：' + res.msg);
        }
    };

    // 滚动模式下：新增坑位
    const createPageCell = async (pageSectionCode: string) => {
        // 拿到楼层对应的坑位列表
        const pageSection: PageSection = elementMap.get(pageSectionCode);
        const pageCellList = pageSection.pageCellList || [];

        // 找到 layout.rect.left 最大的坑位
        let maxLeftCell = pageCellList[0];
        if (pageCellList.length > 0) {
            maxLeftCell = pageCellList.reduce((prev, current) => {
                return (prev.layout.rect.left > current.layout.rect.left) ? prev : current;
            });
        }

        // 构建新坑位数据对象
        const newCellData: Partial<PageCell> = {
            pageId: pageSection.pageId,
            pageCode: pageSection.pageCode,
            pageSectionId: pageSection.id,
            pageSectionCode: pageSection.code,
            orgId: pageSection.orgId,
            // 如果存在最大 left 的坑位，则基于它创建新的坑位布局
            layout: maxLeftCell ? {
                rect: {
                    ...maxLeftCell.layout.rect,
                    left: maxLeftCell.layout.rect.left + (pageSection.layout.gap ?? 0) + (pageSection.layout.standardSize?.width ?? maxLeftCell.layout.rect.width),
                },
                padding: pageSection.layout.padding,
                gap: pageSection.layout.gap,
                mode: pageSection.layout.mode,
                standardSize: pageSection.layout.standardSize,
                props: {},
            } : {
                rect: { top: 0, left: 0, width: 0, height: 0 },
                padding: { top: 0, left: 0, right: 0, bottom: 0 },
                gap: 0,
                mode: DesignMode.SCROLLABLE,
                standardSize: { width: 0, height: 0 },
                props: {},
            },
        };

        const res = await cellApi.createPageCell(newCellData);
        if (res.code === 200) {
            feedback.success('坑位新增成功');
            await refreshPageData(page.value.code);
        } else {
            feedback.error('坑位新增失败：' + res.msg);
        }
    };

    // 新增坑位元素
    const createCellItem = async (pageCellItem: PageCellItem) => {
        const res = await cellApi.createPageCellItem(pageCellItem);
        if (res.code === 200) {
            feedback.success('坑位元素新增成功');
            await refreshPageData(page.value.code);
        } else {
            feedback.error('坑位元素新增失败：' + res.msg);
        }
        return res;
    };

    // 更新坑位元素
    const updateCellItem = async (pageCellItem: PageCellItem) => {
        const res = await cellApi.updatePageCellItem(pageCellItem.code, pageCellItem);
        if (res.code === 200) {
            feedback.success('坑位元素更新成功');
            await refreshPageData(page.value.code);
        } else {
            feedback.error('坑位元素更新失败：' + res.msg);
        }
        return res;
    };

    // 确保坑位有默认坑位元素
    const ensureDefaultCellItem = (pageCell: PageCell) => {
        const hasDefaultItem = pageCell.pageCellItemList?.some(
            item => item.defaultFlag === CELL_ITEM_TYPE.DEFAULT,
        );

        if (!hasDefaultItem) {
            const defaultItem: PageCellItem = {
                code: `item_${Date.now()}-${Math.floor(Math.random() * 1000)}`,
                cellId: pageCell.id,
                cellCode: pageCell.code,
                defaultFlag: CELL_ITEM_TYPE.DEFAULT,
                icons: {},
                type: CELL_ITEM_TYPE.DEFAULT,
                orderNo: 0,
                delFlag: 0,
                status: 1,
                auditStatus: 0,
                visibleStatus: 1,
                onlineStatus: 0,
                scheduleRule: {
                    scheduleConfig: {
                        scheduleType: 0,
                        dayList: [],
                        startTime: '',
                        endTime: '',
                    },
                },
            } as unknown as PageCellItem;

            if (!pageCell.pageCellItemList) {
                pageCell.pageCellItemList = [];
            }
            pageCell.pageCellItemList.push(defaultItem);
        }
    };

    // 对坑位元素进行排序（默认项在前）
    const sortCellItems = (pageCell: PageCell) => {
        if (!pageCell.pageCellItemList) return;

        pageCell.pageCellItemList.sort((a, b) => {
            if (a.defaultFlag === CELL_ITEM_TYPE.DEFAULT) return -1;
            if (b.defaultFlag === CELL_ITEM_TYPE.DEFAULT) return 1;
            return 0;
        });
    };

    // 基础画布引用
    const baseCanvasRef = ref<any>(null);

    // 滚动到指定页面楼层
    const scrollToPageSection = (pageSectionCode: string) => {
        // 使用 nextTick 确保 DOM 已更新
        nextTick(() => {
            // 现在我们可以直接使用 ID 查找对应的页面楼层元素
            const sectionElement = document.getElementById(`section-${pageSectionCode}`);
            if (sectionElement && baseCanvasRef.value) {
                // 更新位置和标尺
                baseCanvasRef.value.updatePositionAndRulers(0, -sectionElement.offsetTop);
            }
        });
    };

    // 注册基础画布引用
    const registerBaseCanvas = (canvas: any) => {
        baseCanvasRef.value = canvas;
    };

    return {
        mode,
        desktopCode,
        navCode,
        page,
        elementMap,
        selectedElementCode,
        selectedElementType,
        selectedElement,
        getPage,
        buildElementMap,
        updatePageSectionOrderNo,
        refreshPageData,
        refreshPage,
        updateProps,
        updateElement,
        batchUpdateCellLayoutProps,
        updateCellComponent,
        switchMode,
        setNavAndDesktop,
        switchElement,
        switchPageCellItem,
        showDeletedPageSections,
        switchDeletedPageSections,
        changePageSection,
        addPageSection,
        getPageSection,
        updatePageSection,
        updatePageCell,
        selectedPageSection,
        selectedPageCell,
        selectedPageCellItem,
        selectedCellItemMap,
        createPageCell,
        createCellItem,
        updateCellItem,
        ensureDefaultCellItem,
        sortCellItems,
        pageSectionSaveAsSection,
        getElementTypeName,
        baseCanvasRef,
        scrollToPageSection,
        registerBaseCanvas,
    };
}, {
    persist: {
        enabled: true,
        storeName: 'design-portal-store',
        driver: STORAGE_DRIVER.INDEXED_DB,
        storeKey: 'page-designer-store',
        serializer: {
            serialize: (state: StateTree): any => {
                return state;
            },
            deserialize: (data: any): StateTree | any => {
                const stateTree: StateTree = data;

                // 恢复 elementMap
                if (stateTree.elementMap) {
                    stateTree.elementMap = reactive(new Map(Object.entries(stateTree.elementMap)));
                } else {
                    stateTree.elementMap = reactive(new Map());
                }

                // 恢复 selectedCellItemMap
                if (stateTree.selectedCellItemMap) {
                    stateTree.selectedCellItemMap = reactive(new Map(Object.entries(stateTree.selectedCellItemMap)));
                } else {
                    stateTree.selectedCellItemMap = reactive(new Map());
                }

                return stateTree;
            },
        },
    },
});
