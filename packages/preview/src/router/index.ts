import type { RouteRecordRaw } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';
import { scopedStyleWrapper } from '@/utils/scoped-style-wrapper';

const routes: Array<RouteRecordRaw> = [
    {
        path: '/demo',
        name: 'DEMO',
        meta: {
            componentName: 'DEMO',
            layout: 'designer',
        },
        component: scopedStyleWrapper(() => import('@/views/demo.vue'), 'Demo'),
    },
];

const router = createRouter({
    history: createWebHistory(),
    routes,
});

export { routes };
export default router;
